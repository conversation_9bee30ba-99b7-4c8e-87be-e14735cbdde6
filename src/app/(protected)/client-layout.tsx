"use client";

import { usePathname } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { BottomNavigation } from "@/components/bottom-navigation";
import { FloatingMascot } from "@/components/floating-mascot";
import { Sidebar } from "@/components/sidebar";
import { ListColorProvider } from "@/contexts/list-color-context";
import { PrimaryColorProvider } from "@/contexts/primary-color-context";
import { TagFilterProvider } from "@/contexts/tag-filter-context";
import { SidebarProvider, useSidebar } from "@/contexts/sidebar-context";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { useAppInitialization } from "@/hooks/use-app-initialization";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";

interface ClientLayoutProps {
  children: React.ReactNode;
  mascotPreference: "golden" | "black";
}

// Inner component that uses sidebar context
function ClientLayoutInner({
  children,
  mascotPreference
}: ClientLayoutProps) {
  const pathname = usePathname();
  const isTasksPage = pathname === "/tasks";
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const { isCollapsed, toggleCollapse } = useSidebar();

  // Initialize app with critical data preloading as early as possible
  useAppInitialization();

  return (
    <div className="flex min-h-screen">
      {/* Desktop Sidebar */}
      {isDesktop && (
        <Sidebar
          isCollapsed={isCollapsed}
          onToggleCollapse={toggleCollapse}
        />
      )}

      {/* Main Content Area */}
      <div className={cn(
        "flex min-h-screen flex-col flex-1 transition-all duration-300",
        isDesktop && !isCollapsed && "ml-64",
        isDesktop && isCollapsed && "ml-16"
      )}>
        {/* Mobile Navigation - only show on mobile and when not on tasks page */}
        {!isDesktop && !isTasksPage && <Navigation />}

        <main className="flex-1 pb-16 md:pb-0">
          <div className={cn(
            isDesktop ? "w-full px-4" : "container-max-width"
          )}>
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </div>
        </main>

        {/* Mobile Bottom Navigation */}
        {!isDesktop && <BottomNavigation />}

        <FloatingMascot
          defaultMascot={mascotPreference}
        />
      </div>
    </div>
  );
}

export function ClientLayout({
  children,
  mascotPreference
}: ClientLayoutProps) {
  return (
    <ErrorBoundary>
      <PrimaryColorProvider>
        <ListColorProvider>
          <TagFilterProvider>
            <SidebarProvider>
              <ClientLayoutInner
                mascotPreference={mascotPreference}
              >
                {children}
              </ClientLayoutInner>
            </SidebarProvider>
          </TagFilterProvider>
        </ListColorProvider>
      </PrimaryColorProvider>
    </ErrorBoundary>
  );
}
