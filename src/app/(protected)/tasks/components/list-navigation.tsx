"use client";

import { useState, useRef, useEffect } from "react";
import { List, Tag } from "@/lib/db";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Plus, Undo, Redo, Edit3, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { getListColorByValue, getContrastTextColor } from "@/lib/list-colors";
import { SkeletonListNavigation } from "@/components/ui/skeleton";
import { TagsDropdown } from "@/components/ui/tags-dropdown";
import { TagFilter } from "@/lib/types";
import { renderSpaceIcon } from "@/lib/space-icons";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSidebar } from "@/contexts/sidebar-context";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  restrictToHorizontalAxis,
  restrictToFirstScrollableAncestor,
} from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface UnderlineIndicatorProps {
  lists: List[];
  currentListId: string | null;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  activeTag?: TagFilter | null;
}

function UnderlineIndicator({ lists, currentListId, scrollContainerRef, activeTag }: UnderlineIndicatorProps) {
  // This component manages bidirectional underline animations
  // The actual underlines are rendered as pseudo-elements on each tab button

  const previousListIdRef = useRef<string | null>(null);
  const previousActiveTagRef = useRef<TagFilter | null>(null);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const previousListId = previousListIdRef.current;
    const previousActiveTag = previousActiveTagRef.current;

    // Determine color based on active tag or current list
    let color = '#6b7280'; // Default fallback
    let targetElementSelector = '';

    if (activeTag) {
      // Use tag color when tag filtering is active
      color = activeTag.color;
      targetElementSelector = '[data-tag-filter]';
    } else if (currentListId) {
      // Use list color when no tag filter is active
      const currentList = lists.find(list => list.id === currentListId);
      color = currentList?.color || '#6b7280';
      targetElementSelector = `[data-tab-id="${currentListId}"]`;
    } else {
      return; // No active list or tag
    }

    // Clear any existing animation timeout
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }

    // Helper function to find elements
    const findTabButton = (listId: string) => {
      return container.querySelector(`[data-tab-id="${listId}"]`) as HTMLElement;
    };
    const findTagButton = () => {
      return container.querySelector('[data-tag-filter]') as HTMLElement;
    };

    // Clear all animation states first
    const allElements = container.querySelectorAll('[data-tab-id], [data-tag-filter]');
    allElements.forEach((element) => {
      (element as HTMLElement).removeAttribute('data-departing');
      (element as HTMLElement).removeAttribute('data-arriving');
    });

    // Determine if we're switching between tag and list modes
    const wasTagMode = previousActiveTag !== null;
    const isTagMode = activeTag !== null;
    const modeChanged = wasTagMode !== isTagMode;

    if (modeChanged) {
      // Mode change: tag <-> list
      let departureOrigin = 'center';
      let arrivalOrigin = 'center';

      // Set CSS custom properties for animation origins and color
      container.style.setProperty('--departure-animation-origin', departureOrigin);
      container.style.setProperty('--active-underline-color', color);
      container.style.setProperty('--underline-animation-origin', arrivalOrigin);

      // Phase 1: Start departure animation for previous element
      let previousElement: HTMLElement | null = null;
      if (wasTagMode) {
        previousElement = findTagButton();
      } else if (previousListId) {
        previousElement = findTabButton(previousListId);
      }

      if (previousElement) {
        previousElement.setAttribute('data-departing', 'true');
      }

      // Phase 2: After departure completes, start arrival animation
      animationTimeoutRef.current = setTimeout(() => {
        // Remove departure state
        if (previousElement) {
          previousElement.removeAttribute('data-departing');
        }

        // Start arrival animation for current element
        let currentElement: HTMLElement | null = null;
        if (isTagMode) {
          currentElement = findTagButton();
        } else if (currentListId) {
          currentElement = findTabButton(currentListId);
        }

        if (currentElement) {
          currentElement.setAttribute('data-arriving', 'true');
        }

        // Clean up arrival state after animation completes
        setTimeout(() => {
          if (currentElement) {
            currentElement.removeAttribute('data-arriving');
          }
        }, 150);
      }, 150);
    } else if (!isTagMode && previousListId && previousListId !== currentListId) {
      // List-to-list navigation (existing logic)
      const previousIndex = lists.findIndex(list => list.id === previousListId);
      const currentIndex = lists.findIndex(list => list.id === currentListId);

      if (previousIndex !== -1 && currentIndex !== -1) {
        let departureOrigin = 'center';
        let arrivalOrigin = 'center';

        if (currentIndex > previousIndex) {
          departureOrigin = 'right';
          arrivalOrigin = 'left';
        } else if (currentIndex < previousIndex) {
          departureOrigin = 'left';
          arrivalOrigin = 'right';
        }

        container.style.setProperty('--departure-animation-origin', departureOrigin);
        container.style.setProperty('--active-underline-color', color);
        container.style.setProperty('--underline-animation-origin', arrivalOrigin);

        const previousTabButton = findTabButton(previousListId);
        if (previousTabButton) {
          previousTabButton.setAttribute('data-departing', 'true');
        }

        animationTimeoutRef.current = setTimeout(() => {
          if (previousTabButton) {
            previousTabButton.removeAttribute('data-departing');
          }

          const currentTabButton = findTabButton(currentListId);
          if (currentTabButton) {
            currentTabButton.setAttribute('data-arriving', 'true');
          }

          setTimeout(() => {
            if (currentTabButton) {
              currentTabButton.removeAttribute('data-arriving');
            }
          }, 150);
        }, 150);
      }
    } else {
      // Initial load or same element - just set the color and center origin
      container.style.setProperty('--active-underline-color', color);
      container.style.setProperty('--underline-animation-origin', 'center');
    }

    // Update the previous references
    previousListIdRef.current = currentListId;
    previousActiveTagRef.current = activeTag;
  }, [currentListId, lists, activeTag]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  // This component doesn't render anything - the underlines are CSS pseudo-elements
  return null;
}

interface ListNavigationProps {
  lists: List[];
  currentListId: string | null;
  taskCounts: Record<string, number>;
  onListSelect: (list: List) => void;
  onListsReorder: (reorderedLists: List[]) => void;
  onAddListClick: () => void;
  useContainer?: boolean;
  // Inline editing props
  isInlineEditEnabled?: boolean;
  onInlineEditToggle?: () => void;
  // Undo/Redo props
  canUndo?: boolean;
  canRedo?: boolean;
  undo?: () => Promise<void>;
  redo?: () => Promise<void>;
  getLastAction?: () => any;
  getLastRedoAction?: () => any;
  // Loading props
  isLoading?: boolean;
  showSkeleton?: boolean;
  // Tags dropdown props
  availableTags?: Tag[];
  onTagSelect?: (tag: TagFilter) => void;
  onTagCreate?: (name: string, color: string) => Promise<Tag | null>;
  onSearchTags?: (searchTerm: string) => Promise<Tag[]>;
  activeTag?: TagFilter | null;
  onClearTagFilter?: () => void;
}

interface SortableTabProps {
  list: List;
  isActive: boolean;
  taskCount: number;
  onClick: () => void;
  isInlineEditEnabled?: boolean;
  isAnimating?: boolean; // Track if we're in the middle of an animation
  isDragActive?: boolean; // Track if any list is being dragged
  draggedListId?: string | null; // Track which specific list is being dragged
}



function SortableTab({ list, isActive, taskCount, onClick, isInlineEditEnabled, isAnimating = false, isDragActive = false, draggedListId = null }: SortableTabProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: list.id });

  const style = {
    transform: CSS.Transform.toString(transform),
  };

  // Check if this specific list is being dragged (either from useSortable or from global state)
  const isThisListDragging = isDragging || (isDragActive && draggedListId === list.id);

  const listColor = getListColorByValue(list.color);
  const hasColor = list.color !== null;

  // Get text color for active state - use list color if available
  const getActiveTextColor = (): string => {
    if (!hasColor || !list.color) {
      return "var(--muted-foreground)"; // Use muted color for colorless lists
    }
    return list.color; // Use list color for colored lists
  };

  const getBadgeStyles = () => {
    if (!hasColor || !list.color) {
      return {
        backgroundColor: "#e9ecef",
        color: "#4b5563", // Brighter text color for badges
        opacity: 0.8,
      };
    }

    const textColor = getContrastTextColor(list.color);
    return {
      backgroundColor: list.color,
      color: textColor,
      opacity: 0.8,
    };
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        "flex-shrink-0 relative cursor-pointer select-none draggable-in-scroll",
        isThisListDragging && "z-50 opacity-0", // Hide original element during drag
        isDragging && "list-tab-dragging"
      )}
      data-list-id={list.id}
    >
      <button
        type="button"
        onClick={() => {
          // Always call onClick for navigation, regardless of edit mode
          // Tab clicks should only switch lists, not start editing
          onClick();
        }}
        className={cn(
          "flex items-center gap-0.5 px-2 py-1.5 text-sm font-medium rounded-lg transition-colors whitespace-nowrap relative",
          "border border-transparent",
          !isActive && "text-muted-foreground hover:text-foreground hover:bg-muted/50",
          isActive && "font-semibold tab-with-underline", // Make active tab text slightly bolder and add underline
          isThisListDragging && "opacity-50"
        )}
        style={isActive ? { color: getActiveTextColor() } : undefined}
        data-tab-id={list.id}
      >
        {/* Icon to the left of name */}
        {list.icon ? (
          <span
            className={cn(
              "mr-0.5 inline-flex items-center justify-center",
              !(isActive && list.color) && "text-muted-foreground"
            )}
          >
            {renderSpaceIcon(list.icon, "h-3.5 w-3.5")}
          </span>
        ) : null}
        <span className="relative inline-block tab-label"><span className="truncate max-w-[120px] block">{list.name}</span></span>
        {!isActive && taskCount > 0 && !isAnimating && (
          <Badge
            className="text-xs border-transparent ml-0.5"
            style={getBadgeStyles()}
          >
            {taskCount}
          </Badge>
        )}
      </button>
    </div>
  );
}



export function ListNavigation({
  lists,
  currentListId,
  taskCounts,
  onListSelect,
  onListsReorder,
  onAddListClick,
  useContainer = true,
  isInlineEditEnabled = false,
  onInlineEditToggle,
  canUndo,
  canRedo,
  undo,
  redo,
  getLastAction,
  getLastRedoAction,
  isLoading = false,
  showSkeleton = false,
  availableTags = [],
  onTagSelect,
  onTagCreate,
  onSearchTags,
  activeTag = null,
  onClearTagFilter,
}: ListNavigationProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [draggedListId, setDraggedListId] = useState<string | null>(null);
  const [activeList, setActiveList] = useState<List | null>(null); // Track the list being dragged for overlay
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const { isCollapsed } = useSidebar();
  const [isScrolling, setIsScrolling] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false); // Track animation state for badge timing
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const dragTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastDragOverRef = useRef<string | null>(null);
  const lastScrollTimeRef = useRef<number>(0);
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);



  // Arrow navigation state
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // Custom modifier to restrict dragging within the navigation container bounds
  const restrictToNavigationContainer = (args: any) => {
    const { transform, containerNodeRect, draggingNodeRect } = args;

    if (!containerNodeRect || !draggingNodeRect || !scrollContainerRef.current) {
      return transform;
    }

    const containerRect = scrollContainerRef.current.getBoundingClientRect();
    const newListButton = scrollContainerRef.current.querySelector('[data-new-list-button]');

    // Calculate boundaries
    const leftBoundary = containerRect.left;
    const rightBoundary = newListButton
      ? newListButton.getBoundingClientRect().left - 8 // 8px gap before "New List" button
      : containerRect.right;

    // Apply horizontal constraints
    const constrainedX = Math.max(
      leftBoundary - draggingNodeRect.left,
      Math.min(rightBoundary - draggingNodeRect.right, transform.x)
    );

    return {
      ...transform,
      x: constrainedX,
    };
  };

  const sensors = useSensors(
    // Mouse sensor for desktop with minimal distance to allow for precise clicking
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 3, // Small distance to prevent accidental drags on click
      },
    }),
    // Touch sensor for mobile with press-and-hold to avoid conflicts with swipe navigation
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200, // 200ms press-and-hold to distinguish from swipe gestures
        tolerance: 8, // Allow 8px of movement during the delay for better UX
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  // Add scroll detection to prevent drag activation during scrolling
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const now = Date.now();
      lastScrollTimeRef.current = now;
      setIsScrolling(true);

      // Clear existing timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // Set scrolling to false after scroll ends
      scrollTimeoutRef.current = setTimeout(() => {
        setIsScrolling(false);
      }, 100); // 100ms after scroll ends
    };

    scrollContainer.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Track isDragging state changes for debugging (can be removed in production)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('isDragging state changed:', isDragging);
    }
  }, [isDragging]);

  // Manage animation state for badge timing
  useEffect(() => {
    if (!currentListId) return;

    // Clear any existing animation timeout
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }

    // Start animation state (hide badges during transition)
    setIsAnimating(true);

    // End animation state as soon as possible for maximum snappiness
    animationTimeoutRef.current = setTimeout(() => {
      setIsAnimating(false);
    }, 50); // Minimal delay to prevent jarring, maximum snappiness
  }, [currentListId]);

  // Scroll to active tab when currentListId changes (but not when tag filtering is active or when clearing tag filter)
  const previousActiveTagRef = useRef<TagFilter | null>(null);

  useEffect(() => {
    const wasTagActive = previousActiveTagRef.current !== null;
    const isTagActive = activeTag !== null;
    const justClearedTag = wasTagActive && !isTagActive;

    // Don't scroll if:
    // 1. Tag filtering is currently active
    // 2. We just cleared a tag filter (to preserve scroll position)
    if (currentListId && !activeTag && !justClearedTag && scrollContainerRef.current) {
      const activeTab = scrollContainerRef.current.querySelector(
        `[data-list-id="${currentListId}"]`
      );
      if (activeTab) {
        activeTab.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        });
      }
    }

    // Update the previous tag reference
    previousActiveTagRef.current = activeTag;
  }, [currentListId, activeTag]);

  const handleDragStart = (event: DragStartEvent) => {
    // Prevent drag activation if user is actively scrolling
    const now = Date.now();
    const timeSinceLastScroll = now - lastScrollTimeRef.current;

    if (isScrolling || timeSinceLastScroll < 150) {
      // Cancel drag if actively scrolling or very recently scrolled
      // Increased timeout to better avoid conflicts with swipe navigation
      return;
    }

    // Additional check for mobile: ensure this isn't part of a swipe gesture
    if (window.innerWidth <= 767) {
      const target = event.active.node?.current;
      if (target) {
        // Temporarily disable swipe navigation during drag
        target.style.touchAction = 'none';
      }
    }

    setIsDragging(true);
    setDraggedListId(event.active.id as string);
    lastDragOverRef.current = null;

    // Find and set the active list for the drag overlay
    const draggedList = lists.find(list => list.id === event.active.id);
    setActiveList(draggedList || null);

    // Add haptic feedback on mobile if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;

    // Only provide haptic feedback when moving to a new target
    if (over && over.id !== lastDragOverRef.current) {
      lastDragOverRef.current = over.id as string;

      // Add haptic feedback when dragging over a new list
      if ('vibrate' in navigator) {
        navigator.vibrate(30);
      }
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    // Re-enable swipe navigation on mobile
    if (window.innerWidth <= 767) {
      const target = event.active.node?.current;
      if (target) {
        target.style.touchAction = 'pan-x';
      }
    }

    setIsDragging(false);
    setDraggedListId(null);
    setActiveList(null);
    lastDragOverRef.current = null;

    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = lists.findIndex((list) => list.id === active.id);
      const newIndex = lists.findIndex((list) => list.id === over.id);

      const reorderedLists = arrayMove(lists, oldIndex, newIndex);
      onListsReorder(reorderedLists);
    }
  };

  const handleDragCancel = () => {
    // Re-enable swipe navigation on mobile
    if (window.innerWidth <= 767 && draggedListId) {
      const draggedElement = scrollContainerRef.current?.querySelector(`[data-list-id="${draggedListId}"]`);
      if (draggedElement) {
        (draggedElement as HTMLElement).style.touchAction = 'pan-x';
      }
    }

    setIsDragging(false);
    setDraggedListId(null);
    setActiveList(null);
    lastDragOverRef.current = null;
  };

  // Show skeleton loading state
  if (showSkeleton || (isLoading && lists.length === 0)) {
    const skeletonContent = (
      <SkeletonListNavigation tabCount={Math.max(3, lists.length || 3)} />
    );

    if (useContainer) {
      return (
        <div className="bg-background border-b border-border">
          {skeletonContent}
        </div>
      );
    }

    return skeletonContent;
  }

  const content = (
    <div className={cn(
      "h-12 flex items-center gap-1",
      useContainer ? "container-max-width mx-auto px-3" : "px-3",
      isDesktop && !useContainer && !isCollapsed && "pl-64",
      isDesktop && !useContainer && isCollapsed && "pl-16"
    )}>
          {/* Undo/Redo Controls - Left side */}
          {canUndo !== undefined && canRedo !== undefined && undo && redo && (
            <div className="flex-shrink-0 mr-1 mobile-button-fixed">
              <div className="flex items-center gap-0.5">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={undo}
                  disabled={!canUndo}
                  title={canUndo ? `Undo: ${getLastAction?.()?.description} (Ctrl+Z)` : "Nothing to undo (Ctrl+Z)"}
                >
                  <Undo className="h-3.5 w-3.5" />
                  <span className="sr-only">Undo</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={redo}
                  disabled={!canRedo}
                  title={canRedo ? `Redo: ${getLastRedoAction?.()?.description} (Ctrl+Y)` : "Nothing to redo (Ctrl+Y)"}
                >
                  <Redo className="h-3.5 w-3.5" />
                  <span className="sr-only">Redo</span>
                </Button>
              </div>
            </div>
          )}

          {/* Scrollable list tabs with drag and drop */}
          <div className="flex-1 min-w-0">
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDragEnd={handleDragEnd}
                onDragCancel={handleDragCancel}
                modifiers={[restrictToHorizontalAxis, restrictToNavigationContainer]}
              >
                <div className="relative">
                <div
                  ref={scrollContainerRef}
                  className="flex items-center gap-0.5 overflow-x-auto scrollbar-hide mobile-scroll-container scroll-momentum-fix list-navigation-container"
                >
                  {/* List Tabs - Always show */}
                  <SortableContext
                    items={lists.map((list) => list.id)}
                    strategy={horizontalListSortingStrategy}
                  >
                    {lists.map((list) => (
                      <div key={list.id} data-list-id={list.id}>
                        <SortableTab
                          list={list}
                          isActive={!activeTag && list.id === currentListId}
                          taskCount={taskCounts[list.id] || 0}
                          onClick={() => onListSelect(list)}
                          isInlineEditEnabled={isInlineEditEnabled}
                          isAnimating={isAnimating}
                          isDragActive={isDragging}
                          draggedListId={draggedListId}
                        />
                      </div>
                    ))}
                  </SortableContext>

                  {/* Tags Dropdown - Mobile only (inside scrollable area) */}
                  {onTagSelect && onSearchTags && (
                    <div className="flex-shrink-0 ml-1 md:hidden">
                      <TagsDropdown
                        availableTags={availableTags}
                        onTagSelect={onTagSelect}
                        onTagCreate={onTagCreate}
                        onSearchTags={onSearchTags}
                        activeTag={activeTag}
                        onClearFilter={onClearTagFilter}
                        disabled={isDragging}
                      />
                    </div>
                  )}

                  {/* Add New List Button - Mobile only (inside scrollable area) */}
                  <div className="flex-shrink-0 ml-1 md:hidden" data-new-list-button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        if (process.env.NODE_ENV === 'development') {
                          console.log('Button clicked - isDragging:', isDragging);
                        }
                        // Force reset drag state and ensure button is interactive
                        if (isDragging) {
                          if (process.env.NODE_ENV === 'development') {
                            console.log('Force resetting drag state on button click');
                          }
                          setIsDragging(false);
                        }
                        e.currentTarget.style.pointerEvents = 'auto';
                        onAddListClick();
                      }}
                      onTouchStart={(e) => {
                        // Ensure touch events work properly
                        e.currentTarget.style.pointerEvents = 'auto';
                      }}
                      className="flex items-center gap-2 text-gray-600 hover:!text-foreground hover:!bg-muted/50 transition-colors rounded-lg touch-manipulation"
                      disabled={isDragging}
                      type="button"
                      role="button"
                      aria-label="Create new list"
                      style={{
                        pointerEvents: 'auto',
                        touchAction: 'manipulation',
                        WebkitTapHighlightColor: 'transparent'
                      }}
                    >
                      <Plus className="h-4 w-4" />
                      <span>New List</span>
                    </Button>
                  </div>
                </div>
                <UnderlineIndicator
                  lists={lists}
                  currentListId={currentListId}
                  scrollContainerRef={scrollContainerRef}
                  activeTag={activeTag}
                />
              </div>

              {/* Drag Overlay to maintain original appearance during drag */}
              <DragOverlay
                adjustScale={false}
                dropAnimation={null}
                style={{
                  transformOrigin: '0 0',
                  transition: 'none',
                  opacity: 1, // Fully opaque, no see-through effect
                }}
              >
                {activeList ? (
                  <div className="list-drag-overlay">
                    <SortableTab
                      list={activeList}
                      isActive={activeList.id === currentListId}
                      taskCount={taskCounts[activeList.id] || 0}
                      onClick={() => {}} // No-op for overlay
                      isInlineEditEnabled={isInlineEditEnabled}
                      isAnimating={false} // Don't animate badges in overlay
                      isDragActive={false} // Don't apply drag styling to overlay
                      draggedListId={null}
                    />
                  </div>
                ) : null}
              </DragOverlay>
            </DndContext>
          </div>

        {/* Right-aligned controls - Desktop only */}
          <div className="hidden md:flex items-center gap-1 flex-shrink-0">
            {/* Tags Dropdown - Desktop only */}
            {onTagSelect && onSearchTags && (
              <div className="flex-shrink-0">
                <TagsDropdown
                  availableTags={availableTags}
                  onTagSelect={onTagSelect}
                  onTagCreate={onTagCreate}
                  onSearchTags={onSearchTags}
                  activeTag={activeTag}
                  onClearFilter={onClearTagFilter}
                  disabled={isDragging}
                />
              </div>
            )}

            {/* Add New List Button - Desktop only */}
            <div className="flex-shrink-0" data-new-list-button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  if (process.env.NODE_ENV === 'development') {
                    console.log('Button clicked - isDragging:', isDragging);
                  }
                  // Force reset drag state and ensure button is interactive
                  if (isDragging) {
                    if (process.env.NODE_ENV === 'development') {
                      console.log('Force resetting drag state on button click');
                    }
                    setIsDragging(false);
                  }
                  e.currentTarget.style.pointerEvents = 'auto';
                  onAddListClick();
                }}
                onTouchStart={(e) => {
                  // Ensure touch events work properly
                  e.currentTarget.style.pointerEvents = 'auto';
                }}
                className="flex items-center gap-2 text-gray-600 hover:!text-foreground hover:!bg-muted/50 transition-colors rounded-lg touch-manipulation"
                disabled={isDragging}
                type="button"
                role="button"
                aria-label="Create new list"
                style={{
                  pointerEvents: 'auto',
                  touchAction: 'manipulation',
                  WebkitTapHighlightColor: 'transparent'
                }}
              >
                <Plus className="h-4 w-4" />
                <span>New List</span>
              </Button>
            </div>

          </div>

          {/* Inline Edit Toggle Button - Always visible on both mobile and desktop */}
          {onInlineEditToggle && (
            <div className="flex-shrink-0 mobile-button-fixed">
              <Button
                ref={buttonRef}
                variant={isInlineEditEnabled ? "default" : "outline"}
                size="sm"
                onClick={(e) => {
                  if (process.env.NODE_ENV === 'development') {
                    console.log('Edit toggle clicked - isDragging:', isDragging);
                  }
                  // Force reset drag state and ensure button is interactive
                  if (isDragging) {
                    if (process.env.NODE_ENV === 'development') {
                      console.log('Force resetting drag state on edit toggle click');
                    }
                    setIsDragging(false);
                  }
                  e.currentTarget.style.pointerEvents = 'auto';
                  onInlineEditToggle();
                }}
                onTouchStart={(e) => {
                  // Ensure touch events work properly
                  e.currentTarget.style.pointerEvents = 'auto';
                }}
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground touch-manipulation mobile-button-fixed"
                disabled={isDragging}
                type="button"
                role="button"
                aria-label={isInlineEditEnabled ? "Disable inline editing" : "Enable inline editing"}
                title={isInlineEditEnabled ? "Disable inline editing" : "Enable inline editing"}
                style={{
                  pointerEvents: 'auto',
                  touchAction: 'manipulation',
                  WebkitTapHighlightColor: 'transparent'
                }}
              >
                <Edit3 className="h-4 w-4" />
                <span className="sr-only">
                  {isInlineEditEnabled ? "Done" : "Edit"}
                </span>
              </Button>
            </div>
          )}
    </div>
  );

  if (useContainer) {
    return (
      <div className="bg-background border-b border-border">
        {content}
      </div>
    );
  }

  return content;
}
